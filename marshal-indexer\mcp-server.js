#!/usr/bin/env node

/**
 * MCP Server Wrapper for Marshal Indexer
 * Ensures proper initialization and error handling
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync } from 'fs';
import { pathToFileURL } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Ensure we're in the right directory
const serverPath = join(__dirname, 'dist', 'server.js');
const serverURL = pathToFileURL(serverPath).href;

if (!existsSync(serverPath)) {
  console.error('❌ Server not built. Run: npm run build');
  process.exit(1);
}

// Set default PROJECT_PATHS if not provided
if (!process.env.PROJECT_PATHS) {
  // Default to parent directories (dukancard and dukancard-app)
  const parentDir = dirname(__dirname);
  process.env.PROJECT_PATHS = `${join(parentDir, 'dukancard')},${join(parentDir, 'dukancard-app')}`;
  console.error(`📁 Using default PROJECT_PATHS: ${process.env.PROJECT_PATHS}`);
}

console.error('🚀 Starting Marshal Indexer MCP Server...');
console.error(`📁 Project paths: ${process.env.PROJECT_PATHS}`);
console.error(`🔧 Server path: ${serverPath}`);

// Import and run the server using file URL for Windows compatibility
try {
  await import(serverURL);
} catch (error) {
  console.error('❌ Failed to start MCP server:', error);
  process.exit(1);
}
