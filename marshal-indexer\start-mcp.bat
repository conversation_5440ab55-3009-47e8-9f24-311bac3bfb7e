@echo off
REM MCP Server Startup Script for Windows
REM This script ensures proper environment setup and starts the MCP server

echo Starting Marshal Indexer MCP Server...

REM Set the working directory to the marshal-indexer folder
cd /d "%~dp0"

REM Set default PROJECT_PATHS if not already set
if not defined PROJECT_PATHS (
    set "PROJECT_PATHS=C:/web-app/dukancard,C:/web-app/dukancard-app"
    echo Using default PROJECT_PATHS: %PROJECT_PATHS%
)

echo Project paths: %PROJECT_PATHS%

REM Ensure the project is built
if not exist "dist\server.js" (
    echo Building project...
    npm run build
    if errorlevel 1 (
        echo Build failed!
        exit /b 1
    )
)

REM Start the MCP server
echo Starting MCP server...
node dist\server.js
