{"mcpServers": {"supabase": {"command": "cmd", "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"], "alwaysAllow": ["list_projects", "list_tables", "execute_sql", "list_functions", "list_edge_functions"]}, "github.com/upstash/context7-mcp": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": [], "alwaysAllow": ["get-library-docs", "resolve-library-id"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "alwaysAllow": ["sequentialthinking"]}, "Playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"]}, "marshal-indexer": {"command": "node", "args": ["marshal-indexer/dist/server.js"], "cwd": "C:/web-app", "env": {"PROJECT_PATH": "C:/web-app/dukancard, C:/web-app/dukancard-app"}}}}